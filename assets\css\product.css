/*
PDP
========
*/
/*
PDP Notices
========
*/
.single-product:not(.pdp-ajax) .woocommerce:has(ul.woocommerce-error) {
    padding-bottom: 30px;
}
.pdp-ajax.single-product .woocommerce-error {
    max-width: 100%;
    width: 300px;
    margin: 0;
    position: fixed;
    right: 20px;
    bottom: 20px;
    z-index: 10;
    font-size: 14px;
}
.pdp-ajax.single-product .woocommerce-error a {
    display: none;
}
.pdp-ajax.single-product.sticky-b .woocommerce-error {
    bottom: 80px;
}
/* -- PDP Regular Notice -- */
.single-product .woocommerce-error {
    margin: 0 auto;
}
.single-product .woocommerce-message {
    margin-left: auto;
    margin-right: auto;
}
.single-product .woocommerce-message .message-content a.button {
    display: none;
}
@media (min-width: 769px) and (max-width: 1199px ) {
    .woocommerce-message .message-content { 
        max-width: 500px;
    }
}
@media (min-width: 769px) {
    .woocommerce-message .message-inner {
        display: flex;
        align-items: center;
    }
    .woocommerce-message .buttons-wrapper {
        margin-left: auto;
        flex-shrink: 0;
    }
}
/* PDP */
.single-product .woocommerce-message {
    margin-bottom: 0;
}
.single-product:has(.archive-header .woocommerce-message) .content-area {
    padding-top: 2rem;
}
.single-product .woocommerce-message a {
    color: #fff;
    transition: 0.2s all;
}
.single-product .woocommerce-message a:not(.button) {
    font-weight: 600;
}
.single-product .woocommerce:has(.woocommerce-message) {
    margin-left: auto;
    margin-right: auto;
}
@media (min-width: 993px) {
    .single-product .woocommerce:has(.woocommerce-message) {
        padding-right: 2.617924em;
        padding-left: 2.617924em;
    }
}
.single-product .woocommerce-message .buttons-wrapper {
    display: flex;
}
.single-product .woocommerce-message .buttons-wrapper .checkout {
    order: 2;
    margin-left: 1em;
}
.woocommerce-message .button.checkout:after  {
    display: inline-block;
    position: relative;
    top: 3px;
    margin-left: 6px;
    content: "";
    width: 16px;
    height: 16px;
    background: #fff;
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13 9L16 12M16 12L13 15M16 12L8 12M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13 9L16 12M16 12L13 15M16 12L8 12M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    -webkit-mask-size: contain;
            mask-size: contain;
}
@media (max-width: 768px) {
    .single-product .woocommerce-message {
        text-align: center;
    }
    .single-product .woocommerce-message .message-inner:before {
        display: none;
    }
    .single-product .woocommerce-message .message-inner .buttons-wrapper {
        display: inline-block;
        padding-top: 0.5rem;
    }
    .single-product .woocommerce-message .button.cart {
        margin-left: 0;
        padding-left: 0;
        border-left: 0;
    }
    .single-product .woocommerce-message .message-inner .buttons-wrapper a span {
        border-color: #fff;
    }
}
@media (min-width: 993px) {
	body:not(.header-4).theme-demir.sticky-d.single-product:has(.site.overlay) .col-full-nav {
		z-index: 6;
	}
    body:not(.header-4).single-product .col-full-nav {
        z-index: 10;
    }
}
@media only screen and (min-width: 1070px) {
	.single-product .site-content .col-full {
		max-width: inherit;
		margin: 0;
		padding: 0;
	}
}
.product-details-wrapper, .related.products, .woocommerce-Tabs-panel, .upsells.products,
#sspotReviews, .woocommerce-tabs.wc-tabs-wrapper, .yith-wfbt-section.woocommerce, .wc-prl-recommendations {
	/* max-width: 1170px; */
	margin-right: auto;
	margin-left: auto;
}
.product-details-wrapper, .related.products, .upsells.products,
#sspotReviews, .woocommerce-tabs.wc-tabs-wrapper, .yith-wfbt-section.woocommerce, .wc-prl-recommendations {
	padding-right: 1em;
	padding-left: 1em;
}
@media (min-width: 993px) {
	.product-details-wrapper, .related.products, .woocommerce-Tabs-panel, .upsells.products,
	#sspotReviews, .woocommerce-tabs.wc-tabs-wrapper, .yith-wfbt-section.woocommerce, .wc-prl-recommendations {
		width: 100%;
		padding-right: 2.617924em;
		padding-left: 2.617924em;
	}
}
.single-product .content-area {
	width: 100%;
}
@media (max-width: 992px) {
	.single-product .content-area {
		width: calc(100% + 2em);
		margin-left: -1em;
	}
}
.product-details-wrapper {
	width: 100%;
	overflow: hidden;
	padding-bottom: 2rem;
}
body:not(.sticky-atc-open) .product-details-wrapper {
	position: relative;
	z-index: 5;
}
@media (min-width: 993px) and (max-width: 1199px ) {
	.single-product .site-content .col-full {
		padding: 0;
	}
}
@media (min-width: 993px) and (max-width: 1279px ) {
	.woocommerce-Tabs-panel {
		padding-left: 0;
		padding-right: 0;
	}
}
.woocommerce div.product {
	margin-top: 35px;
}
.product {
	position: relative;
}
.header-4 .product.outofstock {
	z-index: 0;
}
/* -- Grid -- */
@media (min-width: 993px) {
	.product .images,
	.product .woocommerce-product-gallery {
		float: left;
	}
	.product .summary {
		position: relative;
		float: right;
	}
	/* -- Wide -- */
	.product .woocommerce-product-gallery,
	.product .images {
		width: 60%;
	}
	.product .summary {
		width: 36%;
	}
	/* -- Skinny -- */
	.pdp-g-skinny .product .woocommerce-product-gallery,
	.pdp-g-skinny .product .images {
		width: 40%;
	}
	.pdp-g-skinny .product .summary {
		width: 56%;
	}
	/* -- Regular -- */
	.pdp-g-regular .product .woocommerce-product-gallery,
	.pdp-g-regular .product .images {
		width: 48%;
	}
	.pdp-g-regular .product .summary {
		width: 48%;
	}
}
@media (min-width: 993px) and (max-width: 1199px ) {
	.product .woocommerce-product-gallery,
	.product .images {
		width: 50%;
	}
	.product .summary {
		width: 46%;
	}
}
/* -- Core Gallery -- */
.product .images,
.product .woocommerce-product-gallery {
	position: relative;
}
body:has(.pswp--open) .site-header {
    z-index: 2;
}
@media (min-width: 993px) {
	body.header-4:has(.pswp--open) .header-4-container {
		z-index: 2;
	}
	body:not(.header-4).sticky-d:has(.pswp--open) .col-full-nav,
	body.sticky-d:has(.pswp--open) .col-full-nav.is_stuck {
		z-index: 5;
	}
}
@media (max-width: 992px) {
	body:not(.mobile-toggled).sticky-m:has(.pswp--open) .site-header {
		z-index: 0;
	}
}
@media (max-width: 992px) {
	.product .images,
	.product .woocommerce-product-gallery {
		margin-bottom: 1rem;
	}
}
.product .woocommerce-product-gallery__wrapper img {
	width: 100%;
	display: block;
}
.product .woocommerce-product-gallery .woocommerce-product-gallery__trigger {
	position: absolute;
	top: 0.875em;
	right: 0.875em;
	width: 2.5em;
	height: 2.5em;
	text-indent: -9999px;
	overflow: hidden;
	z-index: 1;
	border-radius: 50%;
	color: #111;
	background: rgba(255, 255, 255, 0.9);
	border: 2px solid rgba(0, 0, 0, 0.1);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
	transition: all 0.2s;
	display: flex;
	align-items: center;
	justify-content: center;
}
.product .woocommerce-product-gallery .woocommerce-product-gallery__trigger:before {
	display: block;
	width: 15px;
	height: 15px;
	position: absolute;
	left: 9px;
	top: 9px;
	content: "";
	background: #333;
	-webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4 8V4M4 4H8M4 4L9 9M20 8V4M20 4H16M20 4L15 9M4 16V20M4 20H8M4 20L9 15M20 20L15 15M20 20V16M20 20H16' stroke='%23374151' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4 8V4M4 4H8M4 4L9 9M20 8V4M20 4H16M20 4L15 9M4 16V20M4 20H8M4 20L9 15M20 20L15 15M20 20V16M20 20H16' stroke='%23374151' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
	-webkit-mask-size: contain;
	        mask-size: contain;
	-webkit-mask-position: center;
	        mask-position: center;
	-webkit-mask-repeat: no-repeat;
	        mask-repeat: no-repeat;
}
.product .woocommerce-product-gallery .woocommerce-product-gallery__trigger:hover {
	transform: scale3d(1.1, 1.1, 1.1);
	background: rgba(255, 255, 255, 1);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
.woocommerce div.product div.images .woocommerce-product-gallery__trigger::after {
	display: none;
}
.woocommerce div.product div.images .woocommerce-product-gallery__trigger::before {
	height: 15px;
	left: 9px;
	top: 9px;
	width: 15px;
}
.product .woocommerce-product-gallery .flex-viewport {
	margin-bottom: 0.5rem;
	transform-style: preserve-3d;
}
.woocommerce-product-gallery__image:not(.flex-active-slide) img {
	margin-left: -1px;
}
.woocommerce-product-gallery__image:only-child img {
	margin: 0;
}
/* -- Core Gallery Caption -- */
.pswp__caption__center {
	text-align: center;
}
/* -- Core Gallery Arrows -- */
ul.flex-direction-nav {
	display: flex;
	justify-content: space-between;
	position: absolute;
	z-index: 2;
	top: 50%;
	width: 100%;
	margin: -82px 0 0;
	list-style: none;
	pointer-events: none;
}
a.flex-next,
a.flex-prev {
	visibility: hidden;
	color: #111;
	pointer-events: visible;
}
a.flex-next:after,
a.flex-prev:before {
	display: inline-flex;
	width: 32px;
	height: 32px;
	content: "";
	background: #333;
	visibility: visible;
	position: relative;
	opacity: 0;
	transition: all 0.2s ease;
	-webkit-mask-size: contain;
	        mask-size: contain;
}
.woocommerce-product-gallery:hover a.flex-prev:before {
	left: 15px;
	opacity: 1;
}
a.flex-prev:before {
	left: 0;
	-webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 19L8 12L15 5' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 19L8 12L15 5' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
a.flex-next:after {
	right: 0;	
	-webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 5L16 12L9 19' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");	
	        mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 5L16 12L9 19' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.woocommerce-product-gallery:hover a.flex-next:after {
	right: 15px;
	opacity: 1;
}
/* -- Core Gallery Thumbnails -- */
.product .woocommerce-product-gallery .flex-control-thumbs {
	margin: 0;
	text-align: center;
}
.product .woocommerce-product-gallery .flex-control-thumbs li {
	display: inline-block;
	margin: 0 0.25rem 0rem;
	cursor: pointer;
}
.product .woocommerce-product-gallery .flex-control-thumbs li img {
	width: 40px;
	opacity: 0.5;
	transition: all 0.2s;
}
@media (min-width: 993px) {
	.product .woocommerce-product-gallery .flex-control-thumbs li img {
		width: 60px;
	}
}
.product .woocommerce-product-gallery .flex-control-thumbs li img.flex-active,
.product .woocommerce-product-gallery .flex-control-thumbs li:hover img {
	opacity: 1;
}
/* -- Sale -- */
.summary .onsale {
	color: #3bb54a;
	background: transparent;
	font-size: 14px;
}

/* Indirim etiketini gizle */
.summary .entry-summary > span.onsale,
.summary .onsale,
span.onsale {
	display: none !important;
}
/* -- Title -- */
.summary h1 {
	margin-bottom: 0.35rem;
	padding-right: 60px;
	word-break: break-word;
}
@media (max-width: 992px) {
	.summary h1.entry-title {
		font-size: 24px;
		padding-right: 0;
	}
}
/* -- Previous/Next -- */
.demir-product-prevnext {
	display: block;
	position: absolute;
	display: inline-flex;
	top: 12px;
	right: 0px;
}
.demir-product-prevnext a {
	display: inline-flex;
	position: relative;
	color: #333;
}
.site-content .demir-product-prevnext a:hover {
	color: #333;
}
.demir-product-prevnext a:focus-visible {
	border-radius: 99%;
}
.demir-product-prevnext a span.icon {
	margin-left: 3px;
	color: #999;
	font-size: 20px;
	transition: 0.2s stroke;
}
.demir-product-prevnext .title {
	display: block;
	padding: 0.5rem 0.75rem 0.25rem 0.75rem;
	font-size: 12px;
	line-height: 1.4;
}
.demir-product-prevnext .prevnext_price {
	display: block;
	padding-bottom: 10px;
	color: #444;
	font-size: 11px;
	line-height: 1.4;
}
.demir-product-prevnext .prevnext_price del {
	opacity: 0.65;
}
.demir-product-prevnext svg {
	width: 20px;
	height: 20px;
	stroke: #999;
	transition: 0.2s stroke;
}
.demir-product-prevnext a:hover svg {
	stroke: #666;
}
.demir-product-prevnext .tooltip {
	visibility: hidden;
	position: absolute;
	z-index: 2;
	right: 0;
	width: 120px;
	margin-top: 30px;
	opacity: 0;
	background-color: #fff;
	box-shadow: 0 0 3px rgba(0, 0, 0, 0.15);
	font-size: 13px;
	text-align: center;
	transition: all 0.25s ease-out;
	transform: translateY(10px);
}
.demir-product-prevnext .tooltip img {
	display: block;
}
.demir-product-prevnext a:hover .tooltip {
	display: block;
	visibility: visible;
	opacity: 1;
	transform: translateY(0);
}
.demir-product-prevnext a:hover span.icon {
	color: #222;
}
@media (max-width: 992px) {
	.demir-product-prevnext {
		display: none;
	}
}
/* -- Price -- */
.product p.price {
	float: left;
	margin: 0 0 0.5rem 0;
	color: #222;
	font-size: clamp(1rem, 0.873rem + 0.4065vw, 1.125rem); /* 16-18 */
}
.product p.price del {
	color: #72767c;
}
.product p.price p.availability {
	margin: 0;
}
/* -- Rating -- */
.product .woocommerce-product-rating {
	display: flex;
	align-items: center;
	clear: both;
}
.product.product-type-external .woocommerce-product-rating {
	margin-bottom: 1rem;
}
.product .woocommerce-product-rating .star-rating {
	margin-right: 0.6180469716em;
}
.product .woocommerce-product-rating a {
	color: #333;
	font-size: 13px;
	line-height: 1;
}
/* -- Short Description -- */
.woocommerce-product-details__short-description {
	clear: both;
	font-size: 14px;
	line-height: 1.5;
	padding-bottom: 0.5rem;
}
.woocommerce-product-details__short-description p {
	margin-bottom: 0.75rem;
}
.woocommerce-product-details__short-description p:empty,
.woocommerce-product-details__short-description *:last-child {
	margin-bottom: 0;
}
.woocommerce-product-details__short-description + .woocommerce-product-rating,
.summary .clear + .woocommerce-product-details__short-description > details:first-child {
	margin-top: 0.5rem;
}
.woocommerce-product-details__short-description table th,
.woocommerce-product-details__short-description table td {
	padding: 5px 0;
	font-size: 13px;
}
@media (min-width: 782px) {
	.woocommerce-Tabs-panel .wp-block-columns {
		padding-top: 10px;
	}
}
/* -- Stock -- */
.product p.stock {
	clear: both;
	margin-top: 1rem;
	margin-bottom: 0;
	padding-top: 0.85rem;
	border-top: 1px solid #e2e2e2;
	font-size: 13px;
	position: relative;
}
.product p.stock.out-of-stock {
	font-weight: bold;
}
.product p.stock.in-stock:before,
.product p.stock.out-of-stock:before {
	position: relative;
	margin-right: 0.5rem;
	display: inline-flex;
	width: 15px;
	height: 15px;
	content: "";
	-webkit-mask-position: center;
	        mask-position: center;
	-webkit-mask-repeat: no-repeat;
	        mask-repeat: no-repeat;
	-webkit-mask-size: contain;
	        mask-size: contain;
}
.product p.stock.in-stock:before {
	top: 3px;
	background: #0f834d;
	-webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor' stroke-width='2'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M5 13l4 4L19 7' /%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor' stroke-width='2'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M5 13l4 4L19 7' /%3E%3C/svg%3E");
}
.product p.stock.out-of-stock:before {
	top: 3px;
	background: #a61e00;
	-webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6 18L18 6M6 6L18 18' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6 18L18 6M6 6L18 18' stroke='%234A5568' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}
.stock:empty:before {
	display: none;
}
.stock.in-stock {
	color: #0f834d;
}
.stock.out-of-stock {
	color: #a61e00;
}
.product .woocommerce-variation-availability p.stock {
	margin-top: 0;
	padding-bottom: 0.85rem;
}
/* -- Variations -- */
.product table.variations {
	margin: 0;
}
.product table.variations td,
.product table.variations th {
    display: list-item;
    padding: 0;
    list-style: none;
}
.product .summary table.variations .value {
	padding-bottom: 0.5rem;
}
.product .summary table.variations tr:last-child .value {
	padding-bottom: 1rem;
}
.product .summary table.variations select {
	margin-bottom: 0.25rem;
	background-color: #fff;
	font-size: 15px;
}
.reset_variations {
	margin-bottom: 0.75rem;
	padding: 0;
	font-size: 13px;
	text-decoration: underline;
	text-underline-offset: 2px;
}
.variations label {
	display: block;
	margin-bottom: 6px;
	color: #222;
	font-size: 14px;
	font-weight: 600;
}
.variations .selected-variation label span:first-child:after {
    background: #0f834d;
	-webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5 13L9 17L19 7' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5 13L9 17L19 7' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    position: relative;
    margin-left: 6px;
    top: 3px;
    display: inline-block;
    width: 16px;
    height: 16px;
    content: "";
    -webkit-mask-position: center;
            mask-position: center;
    -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
    -webkit-mask-size: contain;
            mask-size: contain;
}
.woocommerce-variation-description:not(:empty) {
	margin-bottom: 0.5rem;
}
.woocommerce-variation-description p {
	font-size: 14px;
}
.woocommerce-variation-description p:only-child {
    margin: 0;
}
.woocommerce-variation-description + .woocommerce-variation-price:not(:empty) {
	margin: 0 0 0.75rem 0;
	color: #111;
}
.woocommerce-variation-price .price del .amount {
	font-size: 13px;
}
.woocommerce-variation-price .price .amount {
	font-size: 16px;
}
/* Table style within summary area */
.summary table {
	font-size: 14px;
}
.summary table th {
	padding: 0.5em 1em 0.5em 0;
}
.summary table td {
	padding: 0.5rem 1rem 0.5rem 0;
}
/* -- Select within summary -- */
.summary select {
    background-color: #fff;
}
/* -- Grouped -- */
table.woocommerce-grouped-product-list tr td {
	border-bottom: 1px solid #eee;
}
table.woocommerce-grouped-product-list tr:first-child td {
	padding-top: 0
}
table.woocommerce-grouped-product-list tr:last-child td {
	border: 0;
}
table.woocommerce-grouped-product-list .woocommerce-grouped-product-list-item__quantity {
	width: 95px;
	padding-top: 0.5rem;
}
table.woocommerce-grouped-product-list td.woocommerce-grouped-product-list-item__price {
	padding-right: 0;
	text-align: right;
}
table.woocommerce-grouped-product-list del {
	opacity: 0.6;
	font-size: 0.85em;
}
table.woocommerce-grouped-product-list td.woocommerce-grouped-product-list-item__label {
	font-size: 13.5px;
}
.product table.woocommerce-grouped-product-list p.stock {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 12px;
	line-height: 1.3;
}
.product table.woocommerce-grouped-product-list p.stock:before,
.product table.woocommerce-grouped-product-list p.stock:after {
	display: none;
}
table.woocommerce-grouped-product-list .button,
table.woocommerce-grouped-product-list .button:hover {
	padding: 0;
	border-bottom: 1px solid #ccc;
	color: #333;
	background-color: transparent;
	font-size: 14px;
}
table.woocommerce-grouped-product-list a {
	color: #222;
}
.product.product-type-grouped .cart .single_add_to_cart_button {
	width: 100%;
	margin-left: 0;
}
/* -- External -- */
.product.product-type-external .cart .single_add_to_cart_button {
	width: 100%;
	margin-left: 0;
}
/* -- Quantity -- */
.product form.cart .quantity {
	float: left;
	width: 80px;
}

.product form.cart .quantity input.qty {
	width: 60%;
	height: 47px;
	padding: 0 12px;
	border: 2px solid #ddd;
	border-radius: 4px;
	font-size: 16px;
	text-align: center;
	background: #fff;
	transition: border-color 0.2s;
}

.product form.cart .quantity input.qty:focus {
	outline: none;
	border-color: #3bb54a;
	box-shadow: 0 0 0 2px rgba(59, 181, 74, 0.1);
}
/* -- Add to Cart -- */
div.product .summary form.cart {
	margin: 0.75rem 0 1rem 0;
}
.cart .single_add_to_cart_button {
	float: left;
	width: calc(100% - 120px);
	height: 52px;
	margin-left: 40px;
	padding-top: 0;
	padding-bottom: 0;
	border-radius: 4px;
	background: #3bb54a;
	font-size: 18px;
	font-weight: 400;
	line-height: 52px;
	text-align: center;
	transition: all 0.2s;
}
.product form.cart .quantity:has(input[type=hidden]) + button.single_add_to_cart_button {
	margin-left: 0px;
	width: 100%;
}
.product form.cart .button,
.ajax_add_to_cart.add_to_cart_button {
	position: relative;
}
.product form.cart .single_add_to_cart_button:after,
.ajax_add_to_cart.add_to_cart_button:after {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -9px;
    margin-left: -9px;
    opacity: 0;
    transition: opacity 0s ease;
    content: "";
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-left-color: #fff;
    border-radius: 50%;
    vertical-align: middle;
 }
.product form.cart .loading.single_add_to_cart_button:after,
.loading.ajax_add_to_cart.add_to_cart_button:after {
	opacity: 1;
	transition: opacity 0.25s ease;
	animation: rotate 450ms infinite linear;
}
.product form.cart .button.loading,
.ajax_add_to_cart.add_to_cart_button.loading {
	color: transparent !important;
}
.product form.cart .button.added:before {
	position: relative;
    top: 4px;
	margin-right: 8px;
	content: "";
	display: inline-block;
	width: 20px;
	height: 20px;
	background: #fff;
}
.single_add_to_cart_button + .added_to_cart {
	display: none;
}
.product .variations_button:after, .product .variations_button:before,
.product form.cart:after, .product form.cart:before {
	display: table;
    content: "";
    clear: both;
}
p#wcpay-payment-request-button-separator,
#wc-stripe-payment-request-button-separator {
	font-size: 13px;
}
#wcpay-payment-request-wrapper + .single_add_to_cart_button,
#wc-stripe-payment-request-wrapper + .single_add_to_cart_button,
p#wc-stripe-payment-request-button-separator:not([style*="display:none;"]) + .single_add_to_cart_button,
p#wcpay-payment-request-button-separator:not([style*="display:none;"]) + .single_add_to_cart_button {
    float: none;
    margin: 0;
    width: 100%;
}
#wcpay-payment-request-wrapper + .single_add_to_cart_button,
#wc-stripe-payment-request-wrapper + .single_add_to_cart_button {
	margin-top: 10px;
}
.wcppec-checkout-buttons {
	position: relative;
	z-index: 1;
}
/* -- Modals wrapper -- */
#demir-modals-wrapper {
	display: grid;
    grid-template-columns: auto auto;
    grid-auto-columns: minmax(0, 1fr);
    grid-auto-flow: column;
    gap: 15px;
}
/* -- PDP Widget -- */
.product-widget {
	clear: both;
	margin-bottom: 1rem;
	padding-top: 0.75rem;
	color: #111;
	font-size: 14px;
}
.outofstock .product-widget {
	display: none;
}
.product-widget .widget p {
	margin-bottom: 0.5rem;
}
.product-widget .widget fieldset p {
	margin-bottom: 0;
}
.product-widget ul {
	margin: 5px 0 0px 0;
}
.product-widget .widget ul li {
	position: relative;
	margin-bottom: 3px;
	margin-left: 22px;
	list-style: none;
	font-size: 13px;
}
.product-widget p, .product-widget ul li, .product-widget fieldset legend {
	font-size: clamp(0.8125rem, 0.749rem + 0.2033vw, 0.875rem); /* 13-14 */
}
.product-widget ul li:before {
	position: absolute;
	top: 2.5px;
	left: -22px;
	content: "";
	display: block;
	width: 14px;
	height: 14px;
	background: #333;
}
.product-widget fieldset {
	margin-top: 0.5em;
	padding-top: 0.5rem;
	padding-bottom: 1rem;
	margin-bottom: 1rem;
	border: 1px solid #e2e2e2;
}
.product-widget fieldset legend {
	display: block;
	margin: 0 auto;
	width: inherit;
	padding: 10px;
	color: #111;
	font-weight: 600;
	text-align: center;
}
/* -- Accordion - uses pdp-accordions.js -- */
.woocommerce-product-details__short-description > details:first-child {
    margin-top: 15px;
}
form.variations_form + .woocommerce-product-details__short-description {
	padding-top: 10px;
}
.summary details summary {
	display: block;
	font-size: 13px;
    border-top: 1px solid #e2e2e2;
    outline-offset: 1px;
    padding-top: 0.6rem;
    padding-bottom: 0.6rem;
	font-weight: 600;
	letter-spacing: 0.3px;
	text-transform: uppercase;
	cursor: pointer;
	position: relative;
	padding-right: 2rem;
	color: #111;
}
.summary details ul {
	margin-left: 15px;
}
.summary details summary::-webkit-details-marker {
   display: none;
}
.summary details summary:focus-visible {
	outline-offset: 1px;
}
.summary details summary:-webkit-details-marker {  
	display: none; 
}
.summary details summary:after {
	-webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M19 9L12 16L5 9' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M19 9L12 16L5 9' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");	
	-webkit-mask-position: center;	
	        mask-position: center;
	-webkit-mask-repeat: no-repeat;
	        mask-repeat: no-repeat;
	-webkit-mask-size: contain;
	        mask-size: contain;
	content: "";
	width: 14px;
	height: 14px;
	display: block;
	background: #111;
	margin-top: -7px;
	transition: transform 0.2s;
	position: absolute;
    right: 0;
    top: 50%;
}
.summary details[open] summary:after {
	transform: scaleY(-1);
}
.summary details .cg-accordion-item {
	padding-bottom: 15px;
	font-size: 14px;
}
.summary details ul:last-child,
.summary details p:last-child {
	margin-bottom: 0;
}
/* -- PDP Icons -- */
.product form.cart .button.added:before,
.product-widget ul li:before {
	-webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
	        mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='%234A5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
	-webkit-mask-position: center;
	        mask-position: center;
	-webkit-mask-repeat: no-repeat;
	        mask-repeat: no-repeat;
	-webkit-mask-size: contain;
	        mask-size: contain;
}
/* -- Default Tabs -- */
.woocommerce-tabs.wc-tabs-wrapper {
	max-width: 100%;
	width: 100%;
	/* border-top: 1px solid #f5f5f5; */
}
.woocommerce-tabs {
	overflow: hidden;
	background-color: #fff;
}
@media (min-width: 993px) {
	.woocommerce-tabs {
		padding-bottom: 2.617924em;
	}
}
.woocommerce-tabs ul.tabs {
	margin: 0;
	padding-top: 1.5rem;
	text-align: center;
	list-style: none;
}
.woocommerce-tabs ul.tabs li {
	position: relative;
	display: inline-block;
}
.woocommerce-tabs ul.tabs li a {
	display: block;
	padding: 0.75rem;
	color: #444;
	font-size: clamp(0.9375rem, 0.874rem + 0.2033vw, 1rem); /* 15-16 */
}
.woocommerce-tabs .tabs li a:after {
	display: block;
	position: absolute;
	top: calc(100% - 8px);
	left: 11px;
	width: calc(100% - 22px);
	border-bottom: 3px solid #ccc;
	content: "";
	transition: transform 0.3s cubic-bezier(0.28, 0.75, 0.22, 0.95);
	transform: scale(0,1);
	transform-origin: right center;
	will-change: transform;
}
.woocommerce-tabs ul.tabs li.active a {
	color: #111;
	font-weight: 600;
}
.woocommerce-tabs ul.tabs li.reviews_tab a {
	position: relative;
	padding-right: 30px;
}
.woocommerce-tabs ul.tabs li a span {
	font-size: 10px;
	position: absolute;
	bottom: 13px;
	right: 4px;
	border-radius: 50%;
	color: #fff;
	height: 20px;
	width: 20px;
	line-height: 20px;
	display: inline-block;
	background-color: #dc9814;
	font-weight: bold;
}
.woocommerce-tabs .tabs li#tab-title-reviews a:after {
	width: calc(100% - 41px);
}
.woocommerce-tabs .tabs li.active a:after {
	/* transform: scale(1,1);
	transform-origin: left center; */
	display: none;
}
.woocommerce-tabs .tabs li.active a:focus-visible:after {
	visibility: hidden;
}
@media (max-width: 600px) {
	.woocommerce-tabs ul.tabs {
		text-align: left;
	}
	.woocommerce-tabs ul.tabs li,
	.woocommerce-tabs ul.tabs li a {
		display: block;
		padding: 0;
	}
	.woocommerce-tabs ul.tabs li {
		padding: 0.7rem 0;
		border-bottom: 1px solid #eee;
	}
	.woocommerce-tabs ul.tabs li a span {
		position: relative;
		top: -2px;
		right: -4px;
		text-align: center;
	}
	.woocommerce-tabs .tabs li a:after {
		display: none;
	}
}
/* -- Tabs Content -- */
.panel.woocommerce-Tabs-panel--additional_information h2:first-of-type,
.panel.woocommerce-Tabs-panel--reviews h2:first-of-type {
	display: none;
}
.woocommerce-tabs .panel:focus {
	outline: 0;
}
.woocommerce-tabs .panel {
	padding-top: 1.5rem;
	padding-bottom: 1.5rem;
}

.woocommerce-Tabs-panel p.title {
	margin-bottom: 5px;
	letter-spacing: 0.5px;
}
.woocommerce-Tabs-panel p.title + h2 {
    margin-top: 0;
}
.woocommerce-Tabs-panel h2 {
    margin-bottom: 0.75rem;
}
.woocommerce-Tabs-panel .wp-block-columns p {
	margin-block-start: 0;
}
.panel.woocommerce-Tabs-panel--description .elementor h2:first-of-type {
	display: block;
}
@media (max-width: 992px) {
	.woocommerce-tabs .entry-content {
		font-size: 14px;
	}
	.woocommerce-tabs .wp-block-spacer {
    	max-height: 10px;
	}
}
@media (max-width: 599px) {
	.woocommerce-Tabs-panel .wp-block-columns {
		margin-bottom: 1.5rem;
	}
	.woocommerce-Tabs-panel .wp-block-columns:last-child {
		margin-bottom: 0;
	}
	div.product .wp-block-column:has(img) {
    	order: -1;
	}
}
/* -- Product Attributes Table -- */
table.woocommerce-product-attributes  {
	font-size: clamp(0.875rem, 0.8115rem + 0.2033vw, 0.9375rem); /* 14-15 */
	margin: 1.5rem auto 0 auto;
}
table.woocommerce-product-attributes th,
table.woocommerce-product-attributes td {
	border-bottom: 1px solid #eee;
	padding: 0.75rem 0;
}
table.woocommerce-product-attributes tr:first-child td,
table.woocommerce-product-attributes tr:first-child th {
	padding-top: 0
}
table.woocommerce-product-attributes td {
	text-align: right;
}
table.woocommerce-product-attributes tr:last-child th,
table.woocommerce-product-attributes tr:last-child td {
	border: none;
}
table.woocommerce-product-attributes th {
	color: #111;
	font-weight: 600;
	min-width: 150px;
}
table.woocommerce-product-attributes p {
	margin-bottom: 0;
}
table.woocommerce-product-attributes a {
	color: #444;
}
/* -- Meta -- */
.product_meta {
	width: 100vw;
	max-width: 100%;
	position: relative;
	left: 72%;
	right: 50%;
	margin-left: -50vw;
	margin-right: -50vw;
	text-align: center;
}
.product_meta:has(span) {
	padding-top: 0.85rem;
	padding-bottom: 0.85rem;
	border-top: 1px solid rgba(0, 0, 0, 0.05);
	font-size: clamp(0.8125rem, 0.749rem + 0.2033vw, 0.875rem); /* 13-14 */
}
.product_meta .posted_in,.product_meta .sku_wrapper,.product_meta .tagged_as {
	display: block;
	margin: 0 8px;
	color: #111;
}
@media (min-width: 993px) {
	.product_meta .posted_in,.product_meta .sku_wrapper,.product_meta .tagged_as {
		display: inline-block;
	}
}
.product_meta .posted_in a,
.product_meta .tagged_as a {
	color: #555;
}
.product_meta .posted_in a:first-child,
.product_meta .tagged_as a:first-child {
	margin-left: 5px;
}
.product_meta .sku_wrapper span {
	margin-left: 5px;
}
.product_meta a:hover {
	color: #000;
}
/* -- Related and Upsells -- */
.related-wrapper {
	background-color: #fff;
}
@media (min-width: 993px) {
	.related-wrapper > section > h2 {
		padding-top: 3rem;
	}
	.related > h2:first-child,.upsells > h2:first-child {
		margin-bottom: 1.75rem;
	}
}
@media (max-width: 992px) {
	.related-wrapper section {
		padding-top: 2rem;
		padding-bottom: 1rem;
	}
	.related-wrapper section + section {
		padding-top: 1rem;
	}
}
/* -- PDP Shortcode -- */
.page .product-details-wrapper,
.page .related.products,
.page .woocommerce-tabs {
	max-width: 100%;
	padding-left: 0;
	padding-right: 0;
}
/* -- Password Protected PDP -- */
.site-main:has(form.post-password-form) {
	max-width: 750px;
    margin: 0 auto;
    padding: 1em 1em 3rem 1em;
    font-size: 15px;
}
.site-main form.post-password-form {
	padding: 2.5rem 2.5rem 2rem 2.5rem;
    background: #fff;
    border: 1px solid #eee;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.04);
}
.site-main form.post-password-form input {
	margin-left: 0.5rem;
}
.site-main form.post-password-form input[type="submit"] {
	height: 40px;
	padding: 0 1.5rem;
}
/* -- WooThumbs -- */
.iconic-woothumbs-demir .zm-viewer,
.iconic-woothumbs-demir .iconic-woothumbs-fullscreen {
	z-index: 2;
}
/* -- Iconic Swatches -- */
.iconic-was-swatches__item {
	margin-right: 10px;
}
.iconic-was-swatches {
	margin: 0px;
}
/* -- Studio Wombat Quantity Rules -- */
.wqm-qty .quantity-nav {
	display: none;
}
.wqm-qty-wrapper select.qty {
	width: 6em;
	text-align: left;
	background-color: #fff;
}
.wqm-qty-wrapper + .button {
	margin-left: 0;
	width: 100%
}
/* -- SaySpot -- */
.sspot-all-reviews.single-product .site-content .col-full {
	background-color: transparent;
}
